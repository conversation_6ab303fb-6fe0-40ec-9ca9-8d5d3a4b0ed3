import 'package:flutter/material.dart';
import '../models/warehouse_data.dart';

class HomeViewModel extends ChangeNotifier {
  WarehouseData _warehouseData = WarehouseData(
    totalValue: '2,458,600',
    totalItems: 1256,
    lowStockItems: 23,
    todayInbound: 45,
    todayOutbound: 32,
  );

  List<FunctionItem> _functionItems = [
    FunctionItem(
      title: '库存总览',
      icon: '📊',
      color: const Color(0xFF4285F4),
      route: '/inventory',
    ),
    FunctionItem(
      title: '入库管理',
      icon: '📥',
      color: const Color(0xFF34A853),
      route: '/inbound',
    ),
    FunctionItem(
      title: '出库管理',
      icon: '📤',
      color: const Color(0xFFEA4335),
      route: '/outbound',
    ),
    FunctionItem(
      title: '盘点管理',
      icon: '📋',
      color: const Color(0xFFFBBC04),
      route: '/stocktaking',
    ),
    FunctionItem(
      title: '商品管理',
      icon: '📦',
      color: const Color(0xFF9C27B0),
      route: '/products',
    ),
    FunctionItem(
      title: '供应商管理',
      icon: '🏢',
      color: const Color(0xFF00BCD4),
      route: '/suppliers',
    ),
    FunctionItem(
      title: '报表统计',
      icon: '📈',
      color: const Color(0xFFFF9800),
      route: '/reports',
    ),
    FunctionItem(
      title: '系统设置',
      icon: '⚙️',
      color: const Color(0xFF607D8B),
      route: '/settings',
    ),
  ];

  List<RecentActivity> _recentActivities = [
    RecentActivity(
      title: '苹果手机入库',
      subtitle: 'iPhone 15 Pro Max',
      time: '10:30',
      amount: '+50台',
      type: 'in',
    ),
    RecentActivity(
      title: '小米电视出库',
      subtitle: '小米电视65寸',
      time: '09:15',
      amount: '-12台',
      type: 'out',
    ),
    RecentActivity(
      title: '华为耳机入库',
      subtitle: 'FreeBuds Pro 3',
      time: '08:45',
      amount: '+100台',
      type: 'in',
    ),
  ];

  WarehouseData get warehouseData => _warehouseData;
  List<FunctionItem> get functionItems => _functionItems;
  List<RecentActivity> get recentActivities => _recentActivities;

  void refreshData() {
    // 模拟数据刷新
    notifyListeners();
  }
}
