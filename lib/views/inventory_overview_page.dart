import 'package:flutter/material.dart';

// 库存总览页面
class InventoryOverviewPage extends StatefulWidget {
  const InventoryOverviewPage({super.key});

  @override
  State<InventoryOverviewPage> createState() => _InventoryOverviewPageState();
}

class _InventoryOverviewPageState extends State<InventoryOverviewPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '库存总览',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.black87),
            onPressed: () {
              // 刷新数据
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTopStatistics(),
              const SizedBox(height: 20),
              _buildInventoryStatus(),
              const SizedBox(height: 20),
              _buildCategoryDistribution(),
              const SizedBox(height: 20),
              _buildTrendChart(),
              const SizedBox(height: 20),
              _buildQuickActions(),
              const SizedBox(height: 20),
              _buildRecentActivities(),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _refreshData() async {
    // 模拟刷新延迟
    await Future.delayed(const Duration(seconds: 1));
    setState(() {
      // 刷新数据
    });
  }

  // 顶部统计卡片
  Widget _buildTopStatistics() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '库存统计',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                '总库存价值',
                '¥2,890,450',
                '',
                Colors.blue[50]!,
                Colors.blue,
                Icons.account_balance_wallet_outlined,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                '商品种类',
                '156',
                '种',
                Colors.green[50]!,
                Colors.green,
                Icons.category_outlined,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                '库存周转率',
                '2.3',
                '次/月',
                Colors.orange[50]!,
                Colors.orange,
                Icons.sync_outlined,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                '库存准确率',
                '98.5',
                '%',
                Colors.purple[50]!,
                Colors.purple,
                Icons.verified_outlined,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    String unit,
    Color backgroundColor,
    Color? iconColor,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, size: 16, color: iconColor),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black54,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              if (unit.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(left: 2, bottom: 1),
                  child: Text(
                    unit,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.black54,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  // 库存状态分布
  Widget _buildInventoryStatus() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '库存状态分布',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatusItem('正常库存', '1,234', '件', Colors.green),
              ),
              Expanded(
                child: _buildStatusItem('库存预警', '45', '件', Colors.orange),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildStatusItem('库存不足', '12', '件', Colors.red)),
              Expanded(child: _buildStatusItem('超储商品', '8', '件', Colors.blue)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(
    String title,
    String count,
    String unit,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.black54),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                count,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                unit,
                style: const TextStyle(fontSize: 10, color: Colors.black54),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 分类分布
  Widget _buildCategoryDistribution() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '分类库存统计',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildCategoryItem('电子产品', 1234, 45, Colors.blue),
          _buildCategoryItem('家用电器', 856, 31, Colors.green),
          _buildCategoryItem('办公用品', 432, 16, Colors.orange),
          _buildCategoryItem('其他', 218, 8, Colors.purple),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(
    String category,
    int count,
    int percentage,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              category,
              style: const TextStyle(fontSize: 14, color: Colors.black87),
            ),
          ),
          Text(
            '$count件',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '$percentage%',
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // 趋势图表
  Widget _buildTrendChart() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '库存动态趋势',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: CustomPaint(
              painter: InventoryTrendPainter(),
              size: const Size(double.infinity, 200),
            ),
          ),
        ],
      ),
    );
  }

  // 快速操作
  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '快速操作',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  '库存盘点',
                  Icons.fact_check_outlined,
                  Colors.blue,
                  () {},
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  '库存调整',
                  Icons.tune_outlined,
                  Colors.green,
                  () {},
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  '导出报表',
                  Icons.file_download_outlined,
                  Colors.orange,
                  () {},
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  '设置预警',
                  Icons.notifications_outlined,
                  Colors.red,
                  () {},
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 24, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 最近活动
  Widget _buildRecentActivities() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '最近库存变动',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildActivityItem(
            'iPhone 15 Pro Max',
            '入库 +15件',
            '09:30',
            Icons.arrow_downward,
            Colors.green,
          ),
          _buildActivityItem(
            '小米电视65寸',
            '出库 -8件',
            '08:45',
            Icons.arrow_upward,
            Colors.red,
          ),
          _buildActivityItem(
            'MacBook Pro 14寸',
            '调整 +2件',
            '昨天',
            Icons.edit_outlined,
            Colors.blue,
          ),
          _buildActivityItem(
            '华为FreeBuds Pro',
            '入库 +20件',
            '昨天',
            Icons.arrow_downward,
            Colors.green,
          ),
          _buildActivityItem(
            'iPad Air 第5代',
            '出库 -5件',
            '前天',
            Icons.arrow_upward,
            Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(
    String name,
    String action,
    String time,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, size: 16, color: color),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                Text(action, style: TextStyle(fontSize: 12, color: color)),
              ],
            ),
          ),
          Text(
            time,
            style: const TextStyle(fontSize: 12, color: Colors.black54),
          ),
        ],
      ),
    );
  }
}

// 库存趋势图表绘制器
class InventoryTrendPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // 假数据：30天的库存变化
    final List<double> inventoryData = [
      2800,
      2850,
      2900,
      2950,
      3000,
      2980,
      2960,
      2940,
      2920,
      2900,
      2880,
      2860,
      2840,
      2820,
      2800,
      2780,
      2760,
      2740,
      2720,
      2700,
      2680,
      2660,
      2640,
      2620,
      2600,
      2580,
      2560,
      2540,
      2520,
      2500,
    ];

    final double chartWidth = size.width - 60;
    final double chartHeight = size.height - 40;
    final double startX = 40;
    final double startY = 20;

    final double maxValue = inventoryData.reduce((a, b) => a > b ? a : b) * 1.1;
    final double minValue = inventoryData.reduce((a, b) => a < b ? a : b) * 0.9;

    // 绘制背景
    _drawBackground(canvas, startX, startY, chartWidth, chartHeight);

    // 绘制网格线
    _drawGridLines(canvas, startX, startY, chartWidth, chartHeight);

    // 绘制区域填充
    _drawAreaFill(
      canvas,
      inventoryData,
      startX,
      startY,
      chartWidth,
      chartHeight,
      maxValue,
      minValue,
    );

    // 绘制折线
    _drawLine(
      canvas,
      inventoryData,
      startX,
      startY,
      chartWidth,
      chartHeight,
      maxValue,
      minValue,
    );

    // 绘制标签
    _drawLabels(
      canvas,
      startX,
      startY,
      chartWidth,
      chartHeight,
      maxValue,
      minValue,
    );
  }

  void _drawBackground(
    Canvas canvas,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
  ) {
    final Paint backgroundPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Colors.grey[50]!, Colors.white],
      ).createShader(Rect.fromLTWH(startX, startY, chartWidth, chartHeight));

    canvas.drawRect(
      Rect.fromLTWH(startX, startY, chartWidth, chartHeight),
      backgroundPaint,
    );
  }

  void _drawGridLines(
    Canvas canvas,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
  ) {
    final Paint gridPaint = Paint()
      ..color = Colors.grey[200]!
      ..strokeWidth = 0.5;

    // 水平网格线
    for (int i = 1; i < 4; i++) {
      final double y = startY + (chartHeight / 4) * i;
      canvas.drawLine(
        Offset(startX, y),
        Offset(startX + chartWidth, y),
        gridPaint,
      );
    }

    // 垂直网格线
    for (int i = 1; i < 6; i++) {
      final double x = startX + (chartWidth / 6) * i;
      canvas.drawLine(
        Offset(x, startY),
        Offset(x, startY + chartHeight),
        gridPaint,
      );
    }
  }

  void _drawAreaFill(
    Canvas canvas,
    List<double> data,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
    double maxValue,
    double minValue,
  ) {
    final Paint fillPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Colors.blue.withValues(alpha: 0.3),
          Colors.blue.withValues(alpha: 0.1),
          Colors.blue.withValues(alpha: 0.05),
        ],
      ).createShader(Rect.fromLTWH(startX, startY, chartWidth, chartHeight));

    final Path fillPath = Path();
    fillPath.moveTo(startX, startY + chartHeight);

    for (int i = 0; i < data.length; i++) {
      final double x = startX + (chartWidth / (data.length - 1)) * i;
      final double y =
          startY +
          chartHeight -
          ((data[i] - minValue) / (maxValue - minValue)) * chartHeight;

      if (i == 0) {
        fillPath.lineTo(x, y);
      } else {
        fillPath.lineTo(x, y);
      }
    }

    fillPath.lineTo(startX + chartWidth, startY + chartHeight);
    fillPath.close();

    canvas.drawPath(fillPath, fillPaint);
  }

  void _drawLine(
    Canvas canvas,
    List<double> data,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
    double maxValue,
    double minValue,
  ) {
    final Paint linePaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final Paint pointPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;

    final Path path = Path();

    for (int i = 0; i < data.length; i++) {
      final double x = startX + (chartWidth / (data.length - 1)) * i;
      final double y =
          startY +
          chartHeight -
          ((data[i] - minValue) / (maxValue - minValue)) * chartHeight;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      // 每5个点显示一个数据点
      if (i % 5 == 0) {
        canvas.drawCircle(Offset(x, y), 3, pointPaint);
        canvas.drawCircle(Offset(x, y), 2, Paint()..color = Colors.white);
      }
    }

    canvas.drawPath(path, linePaint);
  }

  void _drawLabels(
    Canvas canvas,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
    double maxValue,
    double minValue,
  ) {
    final TextStyle labelStyle = TextStyle(
      color: Colors.grey[600],
      fontSize: 10,
    );

    // Y轴标签
    for (int i = 0; i <= 4; i++) {
      final double value = minValue + (maxValue - minValue) / 4 * (4 - i);
      final double y = startY + (chartHeight / 4) * i;

      final TextPainter textPainter = TextPainter(
        text: TextSpan(text: value.toInt().toString(), style: labelStyle),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(startX - 35, y - 6));
    }

    // X轴标签
    final List<String> dateLabels = ['1日', '6日', '12日', '18日', '24日', '30日'];
    for (int i = 0; i < dateLabels.length; i++) {
      final double x = startX + (chartWidth / 5) * i;

      final TextPainter textPainter = TextPainter(
        text: TextSpan(text: dateLabels[i], style: labelStyle),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - 8, startY + chartHeight + 8));
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
