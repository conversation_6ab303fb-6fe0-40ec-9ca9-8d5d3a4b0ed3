import 'package:flutter/material.dart';
import 'product_list_page.dart';

// 商品分类页面
class ProductCategoryPage extends StatefulWidget {
  const ProductCategoryPage({super.key});

  @override
  State<ProductCategoryPage> createState() => _ProductCategoryPageState();
}

class _ProductCategoryPageState extends State<ProductCategoryPage> {
  // 展开状态管理
  final Map<String, bool> _expandedCategories = {};

  // 三级分类数据结构
  final List<CategoryItem> _categories = [
    CategoryItem(
      id: '1',
      name: '电子产品',
      icon: Icons.devices,
      color: Colors.blue,
      itemCount: 1234,
      children: [
        CategoryItem(
          id: '1-1',
          name: '手机通讯',
          icon: Icons.phone_android,
          color: Colors.blue[300]!,
          itemCount: 456,
          children: [
            CategoryItem(
              id: '1-1-1',
              name: '智能手机',
              icon: Icons.smartphone,
              color: Colors.blue[200]!,
              itemCount: 234,
            ),
            CategoryItem(
              id: '1-1-2',
              name: '功能手机',
              icon: Icons.phone,
              color: Colors.blue[200]!,
              itemCount: 45,
            ),
            CategoryItem(
              id: '1-1-3',
              name: '手机配件',
              icon: Icons.headset,
              color: Colors.blue[200]!,
              itemCount: 177,
            ),
          ],
        ),
        CategoryItem(
          id: '1-2',
          name: '电脑办公',
          icon: Icons.computer,
          color: Colors.blue[300]!,
          itemCount: 345,
          children: [
            CategoryItem(
              id: '1-2-1',
              name: '笔记本电脑',
              icon: Icons.laptop,
              color: Colors.blue[200]!,
              itemCount: 123,
            ),
            CategoryItem(
              id: '1-2-2',
              name: '台式电脑',
              icon: Icons.desktop_windows,
              color: Colors.blue[200]!,
              itemCount: 89,
            ),
            CategoryItem(
              id: '1-2-3',
              name: '电脑配件',
              icon: Icons.keyboard,
              color: Colors.blue[200]!,
              itemCount: 133,
            ),
          ],
        ),
        CategoryItem(
          id: '1-3',
          name: '数码影音',
          icon: Icons.camera_alt,
          color: Colors.blue[300]!,
          itemCount: 433,
          children: [
            CategoryItem(
              id: '1-3-1',
              name: '数码相机',
              icon: Icons.photo_camera,
              color: Colors.blue[200]!,
              itemCount: 67,
            ),
            CategoryItem(
              id: '1-3-2',
              name: '摄像设备',
              icon: Icons.videocam,
              color: Colors.blue[200]!,
              itemCount: 34,
            ),
            CategoryItem(
              id: '1-3-3',
              name: '音响设备',
              icon: Icons.speaker,
              color: Colors.blue[200]!,
              itemCount: 332,
            ),
          ],
        ),
      ],
    ),
    CategoryItem(
      id: '2',
      name: '家用电器',
      icon: Icons.home,
      color: Colors.green,
      itemCount: 856,
      children: [
        CategoryItem(
          id: '2-1',
          name: '大家电',
          icon: Icons.kitchen,
          color: Colors.green[300]!,
          itemCount: 234,
          children: [
            CategoryItem(
              id: '2-1-1',
              name: '电视机',
              icon: Icons.tv,
              color: Colors.green[200]!,
              itemCount: 89,
            ),
            CategoryItem(
              id: '2-1-2',
              name: '冰箱',
              icon: Icons.kitchen,
              color: Colors.green[200]!,
              itemCount: 67,
            ),
            CategoryItem(
              id: '2-1-3',
              name: '洗衣机',
              icon: Icons.local_laundry_service,
              color: Colors.green[200]!,
              itemCount: 78,
            ),
          ],
        ),
        CategoryItem(
          id: '2-2',
          name: '小家电',
          icon: Icons.blender,
          color: Colors.green[300]!,
          itemCount: 345,
          children: [
            CategoryItem(
              id: '2-2-1',
              name: '厨房电器',
              icon: Icons.microwave,
              color: Colors.green[200]!,
              itemCount: 156,
            ),
            CategoryItem(
              id: '2-2-2',
              name: '生活电器',
              icon: Icons.air,
              color: Colors.green[200]!,
              itemCount: 123,
            ),
            CategoryItem(
              id: '2-2-3',
              name: '个护健康',
              icon: Icons.health_and_safety,
              color: Colors.green[200]!,
              itemCount: 66,
            ),
          ],
        ),
        CategoryItem(
          id: '2-3',
          name: '智能家居',
          icon: Icons.home_outlined,
          color: Colors.green[300]!,
          itemCount: 277,
          children: [
            CategoryItem(
              id: '2-3-1',
              name: '智能音箱',
              icon: Icons.speaker_group,
              color: Colors.green[200]!,
              itemCount: 89,
            ),
            CategoryItem(
              id: '2-3-2',
              name: '智能照明',
              icon: Icons.lightbulb,
              color: Colors.green[200]!,
              itemCount: 134,
            ),
            CategoryItem(
              id: '2-3-3',
              name: '安防设备',
              icon: Icons.security,
              color: Colors.green[200]!,
              itemCount: 54,
            ),
          ],
        ),
      ],
    ),
    CategoryItem(
      id: '3',
      name: '办公用品',
      icon: Icons.business_center,
      color: Colors.orange,
      itemCount: 432,
      children: [
        CategoryItem(
          id: '3-1',
          name: '办公设备',
          icon: Icons.print,
          color: Colors.orange[300]!,
          itemCount: 156,
          children: [
            CategoryItem(
              id: '3-1-1',
              name: '打印机',
              icon: Icons.local_printshop,
              color: Colors.orange[200]!,
              itemCount: 67,
            ),
            CategoryItem(
              id: '3-1-2',
              name: '复印机',
              icon: Icons.content_copy,
              color: Colors.orange[200]!,
              itemCount: 34,
            ),
            CategoryItem(
              id: '3-1-3',
              name: '扫描仪',
              icon: Icons.scanner,
              color: Colors.orange[200]!,
              itemCount: 55,
            ),
          ],
        ),
        CategoryItem(
          id: '3-2',
          name: '办公文具',
          icon: Icons.edit,
          color: Colors.orange[300]!,
          itemCount: 189,
          children: [
            CategoryItem(
              id: '3-2-1',
              name: '书写工具',
              icon: Icons.create,
              color: Colors.orange[200]!,
              itemCount: 89,
            ),
            CategoryItem(
              id: '3-2-2',
              name: '纸张本册',
              icon: Icons.book,
              color: Colors.orange[200]!,
              itemCount: 67,
            ),
            CategoryItem(
              id: '3-2-3',
              name: '办公收纳',
              icon: Icons.folder,
              color: Colors.orange[200]!,
              itemCount: 33,
            ),
          ],
        ),
        CategoryItem(
          id: '3-3',
          name: '办公家具',
          icon: Icons.chair,
          color: Colors.orange[300]!,
          itemCount: 87,
          children: [
            CategoryItem(
              id: '3-3-1',
              name: '办公桌椅',
              icon: Icons.table_restaurant,
              color: Colors.orange[200]!,
              itemCount: 45,
            ),
            CategoryItem(
              id: '3-3-2',
              name: '文件柜',
              icon: Icons.storage,
              color: Colors.orange[200]!,
              itemCount: 23,
            ),
            CategoryItem(
              id: '3-3-3',
              name: '会议设备',
              icon: Icons.meeting_room,
              color: Colors.orange[200]!,
              itemCount: 19,
            ),
          ],
        ),
      ],
    ),
    CategoryItem(
      id: '4',
      name: '其他',
      icon: Icons.category,
      color: Colors.purple,
      itemCount: 218,
      children: [
        CategoryItem(
          id: '4-1',
          name: '服装鞋帽',
          icon: Icons.checkroom,
          color: Colors.purple[300]!,
          itemCount: 89,
        ),
        CategoryItem(
          id: '4-2',
          name: '日用百货',
          icon: Icons.shopping_basket,
          color: Colors.purple[300]!,
          itemCount: 67,
        ),
        CategoryItem(
          id: '4-3',
          name: '体育用品',
          icon: Icons.sports_basketball,
          color: Colors.purple[300]!,
          itemCount: 62,
        ),
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '商品分类',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.black87),
            onPressed: () {
              // 搜索功能
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          return _buildCategoryItem(_categories[index], 0);
        },
      ),
    );
  }

  // 构建分类项
  Widget _buildCategoryItem(CategoryItem category, int level) {
    final bool isExpanded = _expandedCategories[category.id] ?? false;
    final bool hasChildren = category.children.isNotEmpty;

    return Container(
      margin: EdgeInsets.only(
        bottom: 8,
        left: level * 16.0, // 根据层级缩进
      ),
      child: Column(
        children: [
          GestureDetector(
            onTap: () => _onCategoryTap(category),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.03),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // 图标
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: category.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(category.icon, color: category.color, size: 20),
                  ),
                  const SizedBox(width: 12),
                  // 分类信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category.name,
                          style: TextStyle(
                            fontSize: level == 0 ? 16 : (level == 1 ? 15 : 14),
                            fontWeight: level == 0
                                ? FontWeight.w600
                                : FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${category.itemCount}件商品',
                          style: TextStyle(
                            fontSize: 12,
                            color: category.color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 展开/收起图标或进入图标
                  if (hasChildren)
                    Icon(
                      isExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: Colors.grey[400],
                      size: 24,
                    )
                  else
                    Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.grey[400],
                      size: 16,
                    ),
                ],
              ),
            ),
          ),
          // 子分类
          if (hasChildren && isExpanded)
            ...category.children.map(
              (child) => Padding(
                padding: const EdgeInsets.only(top: 8),
                child: _buildCategoryItem(child, level + 1),
              ),
            ),
        ],
      ),
    );
  }

  // 处理分类点击
  void _onCategoryTap(CategoryItem category) {
    if (category.children.isNotEmpty) {
      // 有子分类，展开/收起
      setState(() {
        _expandedCategories[category.id] =
            !(_expandedCategories[category.id] ?? false);
      });
    } else {
      // 没有子分类，跳转到商品列表页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ProductListPage(
            categoryId: category.id,
            categoryName: category.name,
            categoryColor: category.color,
          ),
        ),
      );
    }
  }
}

// 分类数据模型
class CategoryItem {
  final String id;
  final String name;
  final IconData icon;
  final Color color;
  final int itemCount;
  final List<CategoryItem> children;

  CategoryItem({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    required this.itemCount,
    this.children = const [],
  });
}
