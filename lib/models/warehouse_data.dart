import 'package:flutter/material.dart';

class WarehouseData {
  final String totalValue;
  final int totalItems;
  final int lowStockItems;
  final int todayInbound;
  final int todayOutbound;

  WarehouseData({
    required this.totalValue,
    required this.totalItems,
    required this.lowStockItems,
    required this.todayInbound,
    required this.todayOutbound,
  });
}

class FunctionItem {
  final String title;
  final String icon;
  final Color color;
  final String route;

  FunctionItem({
    required this.title,
    required this.icon,
    required this.color,
    required this.route,
  });
}

class RecentActivity {
  final String title;
  final String subtitle;
  final String time;
  final String amount;
  final String type; // 'in' or 'out'

  RecentActivity({
    required this.title,
    required this.subtitle,
    required this.time,
    required this.amount,
    required this.type,
  });
}
